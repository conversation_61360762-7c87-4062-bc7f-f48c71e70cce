import React, { useState, useEffect, useCallback } from 'react';
import { FormControl, Select, MenuItem, Box } from '@mui/material';
import LanguageIcon from '@mui/icons-material/Language';
import { getWidgetLanguageParams } from '@/utils/commonUtility';
import { REGISTRATION_CAPS, QUESTIONNAIRE_CAPS, BOOKING_CAPS } from '@/utils/constants';
import { availableLanguages } from '@/utils/constants/common';
import { ConfirmationModal } from '@/components';

const langDropdownStyles = {
  select: {
    border: 'none',
    '& .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&:hover .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': { border: 'none' },
    '.css-9ueuxu-MuiSelect-select-MuiInputBase-input-MuiOutlinedInput-input': {
      paddingRight: '0 !important',
    },
  },
  icon: {
    fontSize: 28,
  },
  flexCenter: {
    display: 'flex',
    alignItems: 'center',
  },
};

export const Language = ({
  SK,
  PK,
  widgetType,
  setValue,
  reset,
  strings,
  fetchWidgetCallback,
  getValues,
  watch,
  isFormDirty,
  onSaveBeforeLanguageChange,
  setLanguageChanged,
  onLanguageChange,
  onPendingLanguageChange,
}) => {
  const [showLanguageChangeModal, setShowLanguageChangeModal] = useState(false);
  const [pendingLanguage, setPendingLanguage] = useState(null);
  useEffect(() => {
    const currentLanguage = watch('currentLanguage') || 'en';
    strings.setLanguage(currentLanguage);
    strings.setActiveLanguage(currentLanguage);
  }, [watch('currentLanguage'), strings]);

  const localizedLanguages = availableLanguages.map((lang) => ({
    value: lang.value,
    label: lang.value === 'en' ? strings.english || 'English' : strings.french || 'Français',
  }));
  const applyLanguageGlobally = (language) => {
    setValue('currentLanguage', language);
    strings.setLanguage(language);
    strings.setActiveLanguage(language);
  };

  const resetFieldsForLanguageChange = useCallback(
    (currentValues, newLanguage) => {
      if (widgetType.includes(QUESTIONNAIRE_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          nextButtonText: '',
          previousButtonText: '',
          doneButtonText: '',
          spinnerText: '',
          actionButtonText: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          dynamicIdErrorPage: { heading: '', description: '' },
          saveForLaterHeading: '',
          saveForLaterDescription: '',
          discardHeading: '',
          discardDescription: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      } else if (widgetType.includes(BOOKING_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          bookingSummaryDescription: '',
          actionButtonText: '',
          preConfirmationMessage: '',
          confirmationMessage: '',
          cancellationMessage: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          confirmationHeading: '',
          confirmationDescription: '',
          confirmationButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      } else if (widgetType.includes(REGISTRATION_CAPS)) {
        const updatedValues = {
          ...currentValues,
          currentLanguage: newLanguage,
          clientInformationPageTitle: '',
          clientInformationPageSubtitle: '',
          actionButtonText: '',
          finalPageHeading: '',
          finalPageDescription: '',
          introHeading: '',
          introDescription: '',
          introButton: '',
          individualNotFoundPage: { heading: '', description: '' },
          requestNotFoundPage: { heading: '', description: '' },
          widgetTitle: '',
        };
        if (currentValues.actionData && Array.isArray(currentValues.actionData)) {
          updatedValues.actionData = currentValues.actionData.map((action) => {
            if (action.selectedAction === 'Page') {
              return {
                ...action,
                actionHeading: '',
                actionDescription: '',
              };
            }
            return action;
          });
        }
        reset(updatedValues);
      }
    },
    [widgetType, reset],
  );

  const handleWidgetLanguageChange = useCallback(
    (newLanguage, isUserInitiated = false) => {
      applyLanguageGlobally(newLanguage);

      // Get SK and PK from form values (in case widget was just saved) or from props
      const currentValues = getValues();
      const currentSK = currentValues.SK || SK;
      const currentPK = currentValues.PK || PK;

      if (fetchWidgetCallback && currentSK && currentPK) {
        const params = getWidgetLanguageParams({ SK: currentSK, PK: currentPK, widgetType, language: newLanguage });

        if (params) {
          fetchWidgetCallback(params, (data) => {
            const currentWidgetName = currentValues.name;

            if (data) {
              reset({
                ...data,
                SK: currentSK,
                PK: currentPK,
                currentLanguage: newLanguage,
                name: currentWidgetName || data.name,
              });
              return;
            }

            if (isUserInitiated && setLanguageChanged) {
              setLanguageChanged(true);
            }
            // Reset fields with empty values for the new language
            // Use current values which should include the saved widget data with SK/PK
            resetFieldsForLanguageChange(currentValues, newLanguage);
          });
        }
      } else {
        // For new widgets, also reset fields when language changes
        const currentValues = getValues();
        if (isUserInitiated && setLanguageChanged) {
          setLanguageChanged(true);
        }
        resetFieldsForLanguageChange(currentValues, newLanguage);
      }
    },
    [SK, PK, widgetType, fetchWidgetCallback, getValues, reset, setLanguageChanged, resetFieldsForLanguageChange],
  );
  useEffect(() => {
    onLanguageChange?.(handleWidgetLanguageChange);
  }, [onLanguageChange, handleWidgetLanguageChange]);

  const handleLanguageChange = (event) => {
    const newLanguage = event.target.value;
    const currentLanguage = getValues('currentLanguage');

    if (newLanguage !== currentLanguage) {
      if (isFormDirty) {
        setPendingLanguage(event.target.value);
        setShowLanguageChangeModal(true);
      } else {
        handleWidgetLanguageChange(newLanguage, true);
      }
    }
  };

  const handleConfirm = () => {
    if (pendingLanguage) {
      const newLanguage = pendingLanguage;
      handleWidgetLanguageChange(newLanguage, true);
      setPendingLanguage(null);
    }
    setShowLanguageChangeModal(false);
  };

  const handleSaveAndChangeLanguage = () => {
    if (onSaveBeforeLanguageChange) {
      const wasNewWidget = !SK && !PK;

      if (wasNewWidget && pendingLanguage && onPendingLanguageChange) {
        // For new widgets, notify the container about the pending language change
        // The container will handle the language change after it resets the form
        onPendingLanguageChange(pendingLanguage);
      }

      onSaveBeforeLanguageChange().then(() => {
        if (wasNewWidget && pendingLanguage) {
          // For newly created widgets, the container will handle the language change
          // Just clean up the modal state
          setPendingLanguage(null);
          setShowLanguageChangeModal(false);
        } else {
          // For existing widgets, use the normal flow
          handleConfirm();
        }
      });
    } else {
      handleConfirm();
    }
  };

  return (
    <>
      <FormControl variant="outlined" size="small">
        <Select
          id="current-language-dropdown"
          value={watch('currentLanguage') || 'en'}
          onChange={handleLanguageChange}
          displayEmpty
          inputProps={{ 'aria-label': 'language' }}
          IconComponent={() => null}
          sx={langDropdownStyles.select}
          renderValue={(selected) => (
            <Box sx={langDropdownStyles.flexCenter}>
              <Box component="span" sx={langDropdownStyles.flexCenter}>
                <LanguageIcon color="primary" sx={langDropdownStyles.icon} />
              </Box>
            </Box>
          )}
        >
          {localizedLanguages.map((lang) => (
            <MenuItem key={lang.value} value={lang.value}>
              {lang.label}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      {showLanguageChangeModal && (
        <ConfirmationModal
          modalOpen={showLanguageChangeModal}
          handleClose={() => handleConfirm()}
          handleConfirm={handleSaveAndChangeLanguage}
          heading={strings.unsavedChanges}
          modalDescription={strings.unsavedChangesMessage}
          closeButtonText={strings.discard}
          confirmButtonText={strings.save}
          handleCloseIconClick={() => setShowLanguageChangeModal(false)}
        />
      )}
    </>
  );
};
