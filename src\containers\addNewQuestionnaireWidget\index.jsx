import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Box, Button, Grid, Paper, Stack, Tab, Tabs, Typography, Select, MenuItem, FormControl } from '@mui/material';
import { HeaderWithActions, PanelBorder } from '@/components';
import { Actions } from './component/Actions';
import { useFieldArray, useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { fieldsValidation, schema } from './data/schema';
import { actionFieldsData } from './data/actionFieldsData';
import { IDENTIFIED, QUESTIONNAIRE_CAPS } from '@/utils/constants';
import { extractGUID, generateFieldsList } from '@/utils/commonUtility';
import { pages } from '@/utils/constants/common';
import { Settings } from '@/components/Settings';
import { ClientInformation } from '@/components/ClientInformation';
import { Workflow } from '@/components/Workflow';
import { Internationalization } from '@/components/Internationalization';
import { QuestionnaireSettings } from './QuestionnaireSettings';
import strings from '@/utils/localization';
import { Language } from '@/components/Language';

export const AddNewQuestionnaireWidget = (props) => {
  const {
    handleNavigationCallback,
    existingWidgetData,
    handleSaveOrUpdateWidgetCallback,
    widgetsList,
    questionnaireWidgetFields,
    publicQuestionnaireList,
    privateQuestionnaireList,
    handleQuestionnaireDropdownCallback,
    questionnaireDefinition,
    setQuestionnaireDefinition,
    allIdTypes,
    orgRequiredIdTypes,
    fetchWidgetWithLanguage,
  } = props;

  const {
    SK,
    PK,
    createdAt,
    clientGroup,
    showClientInformation,
    otpVerificationEnabled,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    fields: availableFields,
    saveForLaterPage = {
      heading: '',
      description: '',
    },
    discardPage = {
      heading: '',
      description: '',
    },
    introduction,
    name,
    repository,
    dynamicWidget,
    dynamicIdErrorPage,
    questionnaire,
    report,
    action,
    previousButtonText,
    nextButtonText,
    doneButtonText,
    showProgressBar,
    showProgressPercentage,
    showSignIn,
    spinnerText,
    currentLanguage,
    defaultLanguage,
    widgetTitle,
    identification,
    individualNotFoundPage,
    isConsentRequired,
    selfRegistration,
    byRequestOnly,
    requestNotFoundPage,
  } = existingWidgetData || {};

  const allowedIdentifications = [
    { label: strings.identified, value: 'IDENTIFIED' },
    { label: strings.deidentified, value: 'DEIDENTIFIED' },
    { label: strings.unidentified, value: 'UNIDENTIFIED' },
  ];

  const questionnaireWidgetGUID = extractGUID(SK);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedIdTypes, setSelectedIdTypes] = useState(() => {
    const healthCareField =
      availableFields?.find((field) => field?.code === 'IDENTIFICATION') ||
      questionnaireWidgetFields?.find((field) => field?.code === 'IDENTIFICATION');
    return healthCareField?.idTypes || [];
  });
  const [enableLogin, setEnableLogin] = useState(showSignIn !== undefined ? showSignIn : true);

  const [fieldsList, setFieldsList] = useState(() =>
    generateFieldsList(questionnaireWidgetFields, availableFields, selectedIdTypes),
  );

  const [selectedFields, setSelectedFields] = useState(fieldsList.filter((field) => field.systemRequired));

  const [isReportPageEnabled, setIsReportPageEnabled] = useState(existingWidgetData ? report?.enabled : true);
  const [fieldsValidationError, setFieldsValidationError] = useState('');
  const [isDemographicsEnabled, setIsDemographicsEnabled] = useState(
    showClientInformation === undefined ? true : showClientInformation,
  );
  const [isDynamicWidget, setIsDynamicWidget] = useState(dynamicWidget || false);

  const initialSelectedFieldsRef = useRef(structuredClone(fieldsList.filter((field) => field.checked)));
  const handleWidgetLanguageChangeRef = useRef(null);

  const defaultValues = {
    name: name || '',
    widgetTitle: widgetTitle || '',
    repository: repository || '',
    dynamicWidget: dynamicWidget || false,
    dynamicIdErrorPage: dynamicIdErrorPage || { heading: '', description: '' },
    questionnaire: questionnaire || { shortName: '', artifactId: '' },
    clientGroup: clientGroup || { cambianReferenceData: { name: '' } },
    defaultLanguage: defaultLanguage || 'en',
    currentLanguage: currentLanguage || 'en',
    identification: questionnaireWidgetGUID ? identification : IDENTIFIED,
    clientInformationPageTitle: clientInformationPageTitle || '',
    clientInformationPageSubtitle: clientInformationPageSubtitle || '',
    isOtpVerificationChecked: otpVerificationEnabled || false,
    fields: selectedFields,
    enableLogin: showSignIn !== undefined ? showSignIn : true,
    showProgressBar: showProgressBar || false,
    showProgressPercentage: showProgressPercentage || false,
    isReportPageChecked: report?.enabled === undefined ? true : report?.enabled,
    reportPrintEnabled: report?.enabled === undefined ? true : report?.showPrintIcon,
    reportDownloadEnabled: report?.enabled === undefined ? false : report?.showDownloadIcon,
    reportSaveEnabled: report?.enabled === undefined ? true : report?.showSaveIcon,
    isDemographicsEnabled: showClientInformation !== undefined ? showClientInformation : true,
    isIntroChecked: introduction?.enabled === undefined ? false : introduction?.enabled,
    introHeading: introduction?.enabled ? introduction?.heading : '',
    introDescription: introduction?.enabled ? introduction?.description : '',
    introButton: introduction?.enabled ? introduction?.buttonText : '',
    isActionChecked: action?.enabled || false,
    actionButtonText: action?.actionButton || '',
    actionData: action?.enabled ? action?.metaData?.actionConditions : [actionFieldsData],
    nextButtonText: nextButtonText || '',
    previousButtonText: previousButtonText || '',
    doneButtonText: doneButtonText || '',
    spinnerText: spinnerText || '',
    saveForLaterHeading: saveForLaterPage?.heading || '',
    saveForLaterDescription: saveForLaterPage?.description || '',
    discardHeading: discardPage?.heading || '',
    discardDescription: discardPage?.description || '',
    byRequestOnly: byRequestOnly || false,
    requestNotFoundPage: requestNotFoundPage || { heading: '', description: '' },
    individualNotFoundPage: individualNotFoundPage || { heading: '', description: '' },
    isConsentRequired: isConsentRequired !== undefined ? isConsentRequired : true,
    selfRegister: selfRegistration === undefined ? false : selfRegistration,
  };

  const {
    register,
    watch,
    getValues,
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { isDirty, errors },
    trigger,
    setError,
  } = useForm({
    mode: 'onBlur',
    resolver: yupResolver(schema),
    defaultValues,
    shouldUnregister: false,
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'actionData',
  });

  const nameValue = watch('name', strings.untitledQuestionnaireWidget || 'Untitled Questionnaire Widget');
  const addField = () => {
    append({ ...actionFieldsData, default: false });
  };

  const removeField = (index) => {
    remove(index);
  };

  useEffect(() => {
    if (existingWidgetData) {
      reset(defaultValues);
    }
  }, [existingWidgetData]);

  const validationData = {
    register,
    errors,
    control,
    reset,
    actionData: fields,
    addField,
    removeField,
    setValue,
    getValues,
    trigger,
    watch,
    setError,
  };
  const isFieldsChanged = useMemo(() => {
    const currentInitialFields = initialSelectedFieldsRef.current;
    if (!currentInitialFields || selectedFields.length !== currentInitialFields.length) return true;

    return selectedFields.some((field) => {
      const initialField = currentInitialFields.find((f) => f.code === field.code);
      if (!initialField) return true;

      return (
        field.checked !== initialField.checked ||
        field.isMandatory !== initialField.isMandatory ||
        field.display !== initialField.display ||
        field.allowMultiple !== initialField.allowMultiple ||
        field.position !== initialField.position ||
        (field.code === 'IDENTIFICATION' &&
          JSON.stringify(field.idTypes?.sort()) !== JSON.stringify(initialField.idTypes?.sort()))
      );
    });
  }, [selectedFields, initialSelectedFieldsRef.current]);
  const [languageChanged, setLanguageChanged] = useState(false);

  const isFormDirty = useMemo(
    () => isDirty || isFieldsChanged || languageChanged,
    [isDirty, isFieldsChanged, languageChanged],
  );

  const validateFields = async (selectedFields) => {
    try {
      const result = await fieldsValidation.validate(selectedFields);
      setFieldsValidationError('');
    } catch (error) {
      setFieldsValidationError(error.errors[0]);
      throw new Error(error.errors[0]);
    }
  };

  const handleSave = async (formData) => {
    await validateFields(selectedFields);
    const updatedFields = selectedFields?.map((field) => {
      if (field.code === 'IDENTIFICATION') {
        return { ...field, idTypes: selectedIdTypes };
      }
      return field;
    });

    const widgetData = {
      name: formData.name,
      widgetTitle: formData.widgetTitle,
      otpVerificationEnabled: formData.isOtpVerificationChecked,
      nextButtonText: formData.nextButtonText,
      doneButtonText: formData.doneButtonText,
      previousButtonText: formData.previousButtonText,
      spinnerText: formData.spinnerText,
      showProgressBar: formData.showProgressBar,
      showProgressPercentage: formData.showProgressPercentage,
      showSignIn: enableLogin,
      identification: formData.identification,
      showClientInformation: formData.isDemographicsEnabled,
      clientInformationPageTitle: formData.clientInformationPageTitle || '',
      clientInformationPageSubtitle: formData.clientInformationPageSubtitle || '',
      fields: updatedFields,
      dynamicWidget: formData.dynamicWidget,
      dynamicIdErrorPage: formData.dynamicWidget ? formData.dynamicIdErrorPage : { heading: '', description: '' },
      repository: formData?.repository,
      questionnaire: {
        artifactId: formData.questionnaire?.artifactId,
        shortName: formData.questionnaire?.shortName,
      },
      clientGroup: { ...formData.clientGroup },
      introduction: {
        enabled: formData.isIntroChecked,
        heading: formData?.isIntroChecked ? formData?.introHeading?.trim() : '',
        description: formData?.isIntroChecked ? formData?.introDescription?.trim() : '',
        buttonText: formData.introButton || '',
      },
      report: {
        enabled: formData.isReportPageChecked,
        showDownloadIcon: formData?.reportDownloadEnabled,
        showPrintIcon: formData?.reportPrintEnabled,
        showSaveIcon: formData?.reportSaveEnabled,
      },
      saveForLaterPage: {
        heading: formData?.saveForLaterHeading,
        description: formData?.saveForLaterDescription,
      },
      discardPage: {
        heading: formData?.discardHeading,
        description: formData?.discardDescription,
      },
      action: {
        enabled: formData?.isActionChecked,
        actionButton: formData?.actionButtonText,
        metaData: {
          actionConditions: formData?.actionData,
        },
      },
      individualNotFoundPage: formData.individualNotFoundPage,
      isConsentRequired: formData?.isConsentRequired,
      selfRegistration: formData.selfRegister,
      byRequestOnly: formData.byRequestOnly,
      requestNotFoundPage: formData.requestNotFoundPage,
      defaultLanguage: formData.defaultLanguage,
      currentLanguage: formData.currentLanguage,
    };

    if (SK && PK) {
      widgetData.SK = SK;
      widgetData.PK = PK;
      widgetData.createdAt = createdAt || new Date().toISOString();
    }

    const result = await handleSaveOrUpdateWidgetCallback(widgetData, QUESTIONNAIRE_CAPS);
    if (result.success) {
      // Reset form with the updated data that includes SK and PK
      if (result.widgetDetails) {
        reset(result.widgetDetails);
      }
      setLanguageChanged(false);
    }
  };

  const onValid = async (formData) => {
    await handleSave(formData);
    initialSelectedFieldsRef.current = structuredClone(selectedFields);
  };

  const onError = (errors) => {
    const getErrorElement = (error) => {
      const errorRefName = error?.ref?.name;
      return errorRefName ? document.querySelector(`[name="${errorRefName}"]`) : null;
    };
    for (const key in errors) {
      if (key !== 'actionData') {
        const generalError = getErrorElement(errors[key]);
        if (generalError) {
          generalError.scrollIntoView({ behavior: 'smooth', block: 'center' });
          generalError.focus();
          return;
        }
      }
    }
    if (errors.actionData && Array.isArray(errors.actionData)) {
      for (const actionErrorFields of errors.actionData) {
        for (const fieldKey in actionErrorFields) {
          const actionError = getErrorElement(actionErrorFields[fieldKey]);
          if (actionError) {
            actionError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            actionError.focus();
            return;
          }
        }
      }
    }
  };

  const handleSelectFields = (fields) => {
    const updatedFields = fields
      ?.map((field) => {
        if (field.code === 'IDENTIFICATION') {
          return {
            ...field,
            idTypes: selectedIdTypes,
            checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
            isMandatory: field.isMandatory || false,
          };
        }
        return {
          ...field,
          checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
          isMandatory: field.isMandatory || false,
        };
      })
      .filter((field) => field.checked);
    setSelectedFields(updatedFields);
    validateFields(updatedFields);
  };
  useEffect(() => {
    handleSelectFields(selectedFields);
  }, [selectedIdTypes]);

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  return (
    <Box>
      <HeaderWithActions
        title={nameValue || strings.untitledQuestionnaireWidget}
        actionButtons={[
          <Language
            key="language"
            SK={SK}
            PK={PK}
            widgetType={QUESTIONNAIRE_CAPS}
            setValue={setValue}
            reset={reset}
            strings={strings}
            fetchWidgetCallback={fetchWidgetWithLanguage}
            getValues={getValues}
            watch={watch}
            isFormDirty={isFormDirty}
            onSaveBeforeLanguageChange={() =>
              new Promise((resolve) => {
                handleSubmit((formData) => {
                  onValid(formData).then(() => {
                    resolve();
                  });
                }, onError)();
              })
            }
            setLanguageChanged={setLanguageChanged}
            onLanguageChange={(fn) => {
              handleWidgetLanguageChangeRef.current = fn;
            }}
          />,
          <Button key="save" variant="contained" onClick={handleSubmit(onValid, onError)} disabled={!isFormDirty}>
            {strings.save}
          </Button>,
        ]}
      />
      <PanelBorder>
        <Grid container>
          <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2}>
            <Tabs value={selectedTab} onChange={handleTabChange}>
              <Tab label={strings.settings} disableRipple />
              <Tab label={strings.clientInformation} disableRipple />
              <Tab label={strings.workflow} disableRipple />
              <Tab label={strings.internationalization} disableRipple />
            </Tabs>

            <Paper elevation={3} sx={{ padding: '16px' }}>
              {selectedTab === 0 && (
                <>
                  <Settings validationData={validationData} setEnableLogin={setEnableLogin} />
                  <QuestionnaireSettings
                    existingWidgetData={existingWidgetData}
                    validationData={validationData}
                    publicQuestionnaireList={publicQuestionnaireList}
                    privateQuestionnaireList={privateQuestionnaireList}
                    handleQuestionnaireDropdownCallback={handleQuestionnaireDropdownCallback}
                    setQuestionnaireDefinition={setQuestionnaireDefinition}
                    isReportPageEnabled={isReportPageEnabled}
                    setIsReportPageEnabled={setIsReportPageEnabled}
                    enableLogin={enableLogin}
                    isDynamicWidget={isDynamicWidget}
                    setIsDynamicWidget={setIsDynamicWidget}
                  />
                </>
              )}
              {selectedTab === 1 && (
                <>
                  <ClientInformation
                    existingWidgetData={existingWidgetData}
                    widgetFields={questionnaireWidgetFields}
                    validationData={validationData}
                    fieldsValidationError={fieldsValidationError}
                    handleSelectFields={handleSelectFields}
                    selectedIdTypes={selectedIdTypes}
                    setSelectedIdTypes={setSelectedIdTypes}
                    allIdTypes={allIdTypes}
                    orgRequiredIdTypes={orgRequiredIdTypes}
                    isClientRequired={isDemographicsEnabled}
                    setIsDemographicsEnabled={setIsDemographicsEnabled}
                    isDemographicsEnabled={isDemographicsEnabled}
                    allowedIdentifications={allowedIdentifications}
                    fieldsList={fieldsList}
                    setFieldsList={setFieldsList}
                  />
                </>
              )}
              {selectedTab === 2 && (
                <>
                  <Workflow
                    existingWidgetData={existingWidgetData}
                    validationData={validationData}
                    widgetsList={widgetsList}
                  />
                  <Actions
                    allAvailableWidgetsList={widgetsList}
                    existingWidgetData={existingWidgetData}
                    isDemographicsEnabled={isDemographicsEnabled}
                    questionnaireDefinition={questionnaireDefinition}
                    validationData={validationData}
                    isDynamicWidget={isDynamicWidget}
                  />
                </>
              )}
              {selectedTab === 3 && (
                <Internationalization
                  existingWidgetData={existingWidgetData}
                  validationData={validationData}
                  handleWidgetLanguageChange={handleWidgetLanguageChangeRef.current}
                />
              )}
            </Paper>
          </Grid>
        </Grid>
      </PanelBorder>
    </Box>
  );
};
