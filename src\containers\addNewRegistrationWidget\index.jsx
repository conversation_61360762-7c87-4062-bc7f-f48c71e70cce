import React, { useEffect, useState, useMemo, useRef } from 'react';
import {
  Box,
  Button,
  Grid,
  Paper,
  Stack,
  TextField,
  Typography,
  Tabs,
  Tab,
  Switch,
  Select,
  MenuItem,
  FormControl,
} from '@mui/material';
import { CambianTooltip, <PERSON>er<PERSON>ithActions, PanelBorder } from '@/components';
import { makeStyles } from '@mui/styles';
import strings from '@/utils/localization';
import { useForm } from 'react-hook-form';
import { fieldsValidation, schema } from './data/schema';
import { yupResolver } from '@hookform/resolvers/yup';
import { EMAIL, IDENTIFIED, PATIENT, REGISTRATION_CAPS } from '@/utils/constants';
import { extractGUID, generateFieldsList } from '@/utils/commonUtility';
import { pages } from '@/utils/constants/common';
import { Settings } from '@/components/Settings';
import { ClientInformation } from '@/components/ClientInformation';
import { Workflow } from '@/components/Workflow';
import { Internationalization } from '@/components/Internationalization';
import { useFieldArray, Controller } from 'react-hook-form';
import { actionFieldsData } from './data/actionFieldsData';
import { Actions } from './components/Actions';
import { Language } from '@/components/Language';

const useStyles = makeStyles((theme) => ({
  container: {
    padding: '8px 48px 48px 48px',
    [theme.breakpoints?.down('sm')]: {
      padding: '8px 16px',
    },
  },
}));

export const AddNewRegistrationWidget = (props) => {
  const {
    registrationWidgetFields,
    handleNavigationCallback,
    existingWidgetData,
    handleSaveOrUpdateWidgetCallback,
    allIdTypes,
    orgRequiredIdTypes,
    widgetsList,
    fetchWidgetWithLanguage,
  } = props;

  const {
    PK,
    SK,
    createdAt,
    name,
    introduction,
    clientInformationPageTitle,
    clientInformationPageSubtitle,
    fields: availableFields,
    identification,
    finalPage,
    defaultLanguage,
    currentLanguage,
    action,
    widgetTitle,
    isConsentRequired,
    showSignIn,
    selfRegistration,
    byRequestOnly,
    requestNotFoundPage,
    individualNotFoundPage,
    otpVerificationEnabled,
  } = existingWidgetData || {};

  const allowedIdentifications = [
    { label: strings.identified, value: 'IDENTIFIED' },
    { label: strings.deidentified, value: 'DEIDENTIFIED' },
  ];

  const [fieldsValidationError, setFieldsValidationError] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [enableLogin, setEnableLogin] = useState(showSignIn !== undefined ? showSignIn : true);
  const [isDemographicsEnabled, setIsDemographicsEnabled] = useState(true);
  const [selectedIdTypes, setSelectedIdTypes] = useState(() => {
    const healthCareField =
      availableFields?.find((field) => field?.code === 'IDENTIFICATION') ||
      registrationWidgetFields?.find((field) => field?.code === 'IDENTIFICATION');
    return healthCareField?.idTypes || [];
  });
  const [fieldsList, setFieldsList] = useState(() =>
    generateFieldsList(registrationWidgetFields, availableFields, selectedIdTypes),
  );
  const [selectedFields, setSelectedFields] = useState(fieldsList.filter((field) => field.systemRequired));

  const initialSelectedFieldsRef = useRef(structuredClone(fieldsList.filter((field) => field.checked)));
  const handleWidgetLanguageChangeRef = useRef(null);
  const classes = useStyles();

  const registrationWidgetGUID = extractGUID(SK);
  const defaultValues = {
    name: registrationWidgetGUID ? name : '',
    widgetTitle: widgetTitle,
    enableLogin: showSignIn === undefined ? true : showSignIn,
    defaultLanguage: defaultLanguage || 'en',
    currentLanguage: currentLanguage || 'en',
    introHeading: registrationWidgetGUID ? introduction?.heading : '',
    introDescription: registrationWidgetGUID ? introduction?.description : '',
    introButton: registrationWidgetGUID ? introduction?.buttonText : '',
    isIntroChecked: introduction?.enabled === undefined ? false : introduction?.enabled,
    identification: registrationWidgetGUID ? identification : IDENTIFIED,
    clientInformationPageTitle: clientInformationPageTitle || '',
    clientInformationPageSubtitle: clientInformationPageSubtitle || '',
    fields: selectedFields,
    finalPageHeading: registrationWidgetGUID ? finalPage?.heading : '',
    finalPageDescription: registrationWidgetGUID ? finalPage?.description : '',
    actionData: action?.enabled ? action?.actionConditions : [actionFieldsData],
    actionButtonText: action?.actionButton || '',
    isActionEnabled: action?.enabled === undefined ? false : action?.enabled,
    isConsentRequired: isConsentRequired !== undefined ? isConsentRequired : true,
    selfRegister: selfRegistration === undefined ? false : selfRegistration,
    byRequestOnly: byRequestOnly || false,
    requestNotFoundPage: requestNotFoundPage || { heading: '', description: '' },
    individualNotFoundPage: individualNotFoundPage || { heading: '', description: '' },
    isOtpVerificationChecked: otpVerificationEnabled || false,
  };

  const {
    register,
    watch,
    control,
    handleSubmit,
    reset,
    setValue,
    getValues,
    trigger,
    setError,
    formState: { isDirty, errors },
  } = useForm({
    mode: 'onBlur',
    resolver: yupResolver(schema),
    defaultValues,
    shouldUnregister: false,
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'actionData',
  });
  const addField = () => {
    append({ ...actionFieldsData, default: false });
  };

  const formData = {
    register,
    errors,
    control,
    reset,
    actionData: fields,
    addField,
    setValue,
    getValues,
    remove,
    trigger,
    watch,
    setError,
  };

  const nameValue = watch('name', strings.untitledRegistrationWidget || 'Untitled Registration Widget');

  useEffect(() => {
    if (existingWidgetData) {
      reset(defaultValues);
    }
  }, [existingWidgetData]);

  const isFieldsChanged = useMemo(() => {
    const currentInitialFields = initialSelectedFieldsRef.current;
    if (!currentInitialFields || selectedFields.length !== currentInitialFields.length) return true;

    return selectedFields.some((field) => {
      const initialField = currentInitialFields.find((f) => f.code === field.code);
      if (!initialField) return true;

      return (
        field.checked !== initialField.checked ||
        field.isMandatory !== initialField.isMandatory ||
        field.display !== initialField.display ||
        field.allowMultiple !== initialField.allowMultiple ||
        field.position !== initialField.position ||
        (field.code === 'IDENTIFICATION' &&
          JSON.stringify(field.idTypes?.sort()) !== JSON.stringify(initialField.idTypes?.sort()))
      );
    });
  }, [selectedFields, initialSelectedFieldsRef.current]);
  const [languageChanged, setLanguageChanged] = useState(false);

  const isFormDirty = useMemo(
    () => isDirty || isFieldsChanged || languageChanged,
    [isDirty, isFieldsChanged, languageChanged],
  );

  const validateFields = async (selectedFields) => {
    try {
      const result = await fieldsValidation.validate(selectedFields);
      setFieldsValidationError('');
    } catch (error) {
      setFieldsValidationError(error.errors[0]);
      throw new Error(error.errors[0]);
    }
  };

  const handleTabChange = (_, newValue) => {
    setSelectedTab(newValue);
  };

  const handleSave = async (formData) => {
    try {
      await validateFields(selectedFields);
      const updatedFields = selectedFields.map((field) => {
        if (field.code === 'IDENTIFICATION') {
          return { ...field, idTypes: selectedIdTypes };
        }
        return field;
      });

      let registrationData = {
        name: formData.name,
        widgetTitle: formData.widgetTitle,
        showSignIn: enableLogin,
        defaultLanguage: formData.defaultLanguage || 'en',
        currentLanguage: formData.currentLanguage || 'en',
        identification: formData.identification,
        clientInformationPageTitle: formData.clientInformationPageTitle || '',
        clientInformationPageSubtitle: formData.clientInformationPageSubtitle || '',
        fields: updatedFields,
        introduction: {
          enabled: formData.isIntroChecked,
          heading: formData.introHeading || '',
          description: formData.introDescription || '',
          buttonText: formData.introButton || '',
        },
        finalPage: {
          heading: formData.finalPageHeading,
          description: formData.finalPageDescription,
        },
        action: {
          enabled: formData.isActionEnabled,
          actionButton: formData?.actionButtonText,
          actionConditions: formData.actionData,
        },
        isConsentRequired: formData?.isConsentRequired,
        selfRegistration: formData.selfRegister,
        individualNotFoundPage: formData.individualNotFoundPage,
        otpVerificationEnabled: formData.isOtpVerificationChecked,
        byRequestOnly: formData.byRequestOnly,
        requestNotFoundPage: formData.requestNotFoundPage,
      };

      if (SK && PK) {
        registrationData.SK = SK;
        registrationData.PK = PK;
        registrationData.createdAt = createdAt || new Date().toISOString();
      }
      console.log('Registration data', registrationData);
      const result = await handleSaveOrUpdateWidgetCallback(registrationData, REGISTRATION_CAPS);
      console.log('registration widget result', result);
      if (result.success) {
        // Reset form with the updated data that includes SK and PK
        if (result.widgetDetails) {
          reset(result.widgetDetails);
        }
        setLanguageChanged(false);
      }
    } catch (err) {
      console.log('Error in Saving registration:', err);
    }
  };

  const handleSelectFields = (fields) => {
    const updatedFields = fields
      .map((field) => {
        if (field.code === 'IDENTIFICATION') {
          return {
            ...field,
            idTypes: selectedIdTypes,
            checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
            isMandatory: field.isMandatory || false,
          };
        }
        return {
          ...field,
          checked: field.checked || (field.systemRequired && field.systemMandatory) || false,
          isMandatory: field.isMandatory || false,
        };
      })
      .filter((field) => field.checked);
    setSelectedFields(updatedFields);
    validateFields(updatedFields);
  };
  useEffect(() => {
    handleSelectFields(selectedFields);
  }, [selectedIdTypes]);

  const onValid = async (formData) => {
    await handleSave(formData);
    initialSelectedFieldsRef.current = structuredClone(selectedFields);
  };

  const onError = (errors) => {
    const getErrorElement = (error) => {
      const errorRefName = error?.ref?.name;
      return errorRefName ? document.querySelector(`[name="${errorRefName}"]`) : null;
    };
    for (const key in errors) {
      if (key !== 'actionData') {
        const generalError = getErrorElement(errors[key]);
        if (generalError) {
          generalError.scrollIntoView({ behavior: 'smooth', block: 'center' });
          generalError.focus();
          return;
        }
      }
    }
    if (errors.actionData && Array.isArray(errors.actionData)) {
      for (const actionErrorFields of errors.actionData) {
        for (const fieldKey in actionErrorFields) {
          const actionError = getErrorElement(actionErrorFields[fieldKey]);
          if (actionError) {
            actionError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            actionError.focus();
            return;
          }
        }
      }
    }
  };

  return (
    <>
      <Box className={classes.container}>
        <HeaderWithActions
          title={nameValue || strings.untitledRegistrationWidget}
          actionButtons={[
            <Language
              key="language"
              SK={SK}
              PK={PK}
              widgetType={REGISTRATION_CAPS}
              setValue={setValue}
              reset={reset}
              strings={strings}
              fetchWidgetCallback={fetchWidgetWithLanguage}
              getValues={getValues}
              watch={watch}
              isFormDirty={isFormDirty}
              onSaveBeforeLanguageChange={() =>
                new Promise((resolve) => {
                  handleSubmit((formData) => {
                    onValid(formData).then(() => {
                      resolve();
                    });
                  }, onError)();
                })
              }
              setLanguageChanged={setLanguageChanged}
              onLanguageChange={(fn) => {
                handleWidgetLanguageChangeRef.current = fn;
              }}
            />,
            <Button
              key="save"
              variant="contained"
              onClick={handleSubmit(onValid, onError)}
              disabled={!isFormDirty}
              id="save"
            >
              {strings.save}
            </Button>,
          ]}
        />
        <PanelBorder>
          <Grid container>
            <Grid item xs={12} sm={12} paddingLeft={2} paddingRight={2}>
              <Tabs value={selectedTab} onChange={handleTabChange}>
                <Tab label={strings.settings} disableRipple />
                <Tab label={strings.clientInformation} disableRipple />
                <Tab label={strings.workflow} disableRipple />
                <Tab label={strings.internationalization} disableRipple />
              </Tabs>

              <Paper elevation={3} sx={{ padding: '16px' }}>
                {selectedTab === 0 && (
                  <>
                    <Settings validationData={formData} setEnableLogin={setEnableLogin} />
                    <Typography variant="body1" sx={{ mt: 3, mb: 2 }}>
                      {strings.registrationMessage}
                      {' *'}
                    </Typography>
                    <Paper
                      sx={{
                        my: 1,
                        border: '1px solid #F0F0F0',
                        borderRadius: '5px 5px 0 0',
                        px: { xs: 2, sm: 3 },
                        py: 2,
                      }}
                    >
                      <Grid container alignItems="center">
                        <Grid item xs={4} lg={3}>
                          <span>
                            {strings.heading}
                            {' *'}
                          </span>
                        </Grid>
                        <Grid item xs={8} lg={9}>
                          <Controller
                            control={control}
                            name={'finalPageHeading'}
                            render={({ field: { ref, ...field } }) => (
                              <TextField
                                {...field}
                                sx={{ maxWidth: '100%' }}
                                fullWidth
                                size="small"
                                placeholder={strings.headingPlaceholder}
                                type="text"
                                name={'finalPageHeading'}
                                onChange={(event) => {
                                  if (event.target.value.trim()) {
                                    field.onChange(event.target.value);
                                  } else {
                                    field.onChange(event.target.value.trim());
                                  }
                                }}
                                error={!!errors?.finalPageHeading}
                                helperText={errors?.finalPageHeading?.message}
                                autoComplete="off"
                              />
                            )}
                          />
                        </Grid>
                      </Grid>
                      <Grid container alignItems="center" sx={{ mt: 2 }}>
                        <Grid item xs={4} lg={3}>
                          <span>
                            {strings.description}
                            {' *'}
                          </span>
                        </Grid>
                        <Grid item xs={8} lg={9}>
                          <Controller
                            control={control}
                            name={'finalPageDescription'}
                            render={({ field: { ref, ...field } }) => (
                              <TextField
                                {...field}
                                sx={{ maxWidth: '100%' }}
                                fullWidth
                                size="small"
                                multiline
                                minRows={6}
                                placeholder={'Description'}
                                type="text"
                                name={'finalPageDescription'}
                                onChange={(event) => {
                                  if (event.target.value.trim()) {
                                    field.onChange(event.target.value);
                                  } else {
                                    field.onChange(event.target.value.trim());
                                  }
                                }}
                                error={!!errors?.finalPageDescription}
                                helperText={errors?.finalPageDescription?.message}
                                autoComplete="off"
                              />
                            )}
                          />
                        </Grid>
                      </Grid>
                    </Paper>
                  </>
                )}
                {selectedTab === 1 && (
                  <>
                    <ClientInformation
                      existingWidgetData={existingWidgetData}
                      widgetFields={registrationWidgetFields}
                      validationData={formData}
                      fieldsValidationError={fieldsValidationError}
                      handleSelectFields={handleSelectFields}
                      selectedIdTypes={selectedIdTypes}
                      setSelectedIdTypes={setSelectedIdTypes}
                      allIdTypes={allIdTypes}
                      orgRequiredIdTypes={orgRequiredIdTypes}
                      isClientRequired={isDemographicsEnabled}
                      setIsDemographicsEnabled={setIsDemographicsEnabled}
                      isDemographicsEnabled={isDemographicsEnabled}
                      allowedIdentifications={allowedIdentifications}
                      fieldsList={fieldsList}
                      setFieldsList={setFieldsList}
                    />
                  </>
                )}
                {selectedTab === 2 && (
                  <>
                    <Workflow
                      existingWidgetData={existingWidgetData}
                      validationData={formData}
                      widgetsList={widgetsList}
                    />

                    <Actions
                      allAvailableWidgetsList={widgetsList}
                      existingWidgetData={existingWidgetData}
                      validationData={formData}
                    />
                  </>
                )}
                {selectedTab === 3 && (
                  <Internationalization
                    existingWidgetData={existingWidgetData}
                    validationData={formData}
                    handleWidgetLanguageChange={handleWidgetLanguageChangeRef.current}
                  />
                )}
              </Paper>
            </Grid>
          </Grid>
        </PanelBorder>
      </Box>
    </>
  );
};
